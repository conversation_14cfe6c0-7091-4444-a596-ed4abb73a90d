import { useContentBatch } from "@/hooks/useContent";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { DynamicContent } from "./content/DynamicContent";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { Plus, Edit, Trash2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { Link } from "react-router-dom";
import * as Icons from "lucide-react";

interface DynamicServiceCardsProps {
  language?: string;
  variant?: "homepage" | "services";
  className?: string;
  title?: string;
  subtitle?: string;
  showTitle?: boolean;
  editable?: boolean;
}

export const DynamicServiceCards = ({
  language = "en",
  variant = "homepage",
  className = "",
  title,
  subtitle,
  showTitle = true,
  editable = false
}: DynamicServiceCardsProps) => {
  const { canEditContent } = useAuth();
  const [isAdding, setIsAdding] = useState(false);

  // Get all service cards dynamically
  const allContent = useQuery(api.content.getAllContent, {
    language: language,
    status: "published"
  });

  // Filter for service cards and sort by order
  const serviceCards = allContent?.filter(item =>
    item.contentType?.name === "service_card"
  ).sort((a, b) => (a.data.order || 0) - (b.data.order || 0)) || [];

  const isLoading = allContent === undefined;

  // Mutations
  const deleteContent = useMutation(api.content.deleteContent);
  const createContent = useMutation(api.content.createNewContent);

  const handleDeleteCard = async (contentId: string) => {
    if (!confirm("Are you sure you want to delete this service card?")) return;

    try {
      await deleteContent({ id: contentId as any });
      toast.success("Service card deleted successfully");
    } catch (error) {
      toast.error("Failed to delete service card");
      console.error("Delete error:", error);
    }
  };

  const handleAddCard = async () => {
    try {
      const newCardData = {
        title: "New Service",
        description: "Service description",
        icon: "Briefcase",
        order: serviceCards.length
      };

      await createContent({
        contentTypeName: "service_card",
        language: language,
        data: newCardData,
        status: "draft"
      });

      toast.success("New service card created");
      setIsAdding(false);
    } catch (error) {
      toast.error("Failed to create service card");
      console.error("Create error:", error);
    }
  };

  const getIconComponent = (iconName: string) => {
    if (!iconName) return Icons.Briefcase;
    const IconComponent = (Icons as any)[iconName];
    return IconComponent || Icons.Briefcase;
  };

  // Loading state
  if (isLoading) {
    return (
      <section className={`py-16 ${variant === "homepage" ? "bg-gray-50" : ""} ${className}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {showTitle && (
            <div className="text-center mb-12">
              <Skeleton className="h-10 w-64 mx-auto mb-4" />
              <Skeleton className="h-6 w-96 mx-auto" />
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 5 }).map((_, i) => (
              <Card key={i} className="bg-white shadow-lg hover:shadow-xl transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start mb-4">
                    <Skeleton className="w-16 h-16 rounded-lg mr-4" />
                    <div className="flex-1">
                      <Skeleton className="h-6 w-32 mb-2" />
                    </div>
                  </div>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  // Get default titles based on variant
  const defaultTitle = variant === "homepage" 
    ? "Our Services" 
    : "Our Main Services";
  
  const defaultSubtitle = variant === "homepage"
    ? "Comprehensive technology solutions designed to secure, connect, and optimize your business operations."
    : "Comprehensive technology solutions organized into key service areas";

  return (
    <section className={`py-16 ${variant === "homepage" ? "bg-gray-50" : ""} ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {showTitle && (
          <div className="text-center mb-12 animate-fade-in-up">
            <div className="flex items-center justify-center gap-4 mb-6">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                {variant === "homepage" ? (
                  <>
                    {title || "Our"} <span className="text-gradient">Services</span>
                  </>
                ) : (
                  title || defaultTitle
                )}
              </h2>
              {editable && canEditContent && (
                <Button
                  onClick={handleAddCard}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Service
                </Button>
              )}
            </div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {subtitle || defaultSubtitle}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {serviceCards.map((card) => {
            const IconComponent = getIconComponent(card.data.icon);
            // Extract slug from identifier and determine the link
            const getServiceLink = (identifier: string) => {
              if (identifier.startsWith('service-')) {
                const slug = identifier.replace('service-', '');
                // Special cases for services that have different routing
                switch (slug) {
                  case 'network':
                    return '/network-solutions';
                  case 'training':
                    return '/training';
                  case 'surveillance':
                    return '/services/electronic-security';
                  default:
                    return `/services/${slug}`;
                }
              }
              return '/contact';
            };

            const CardWrapper = Link;
            const cardProps = { to: getServiceLink(card.identifier) };

            return (
              <CardWrapper key={card._id} {...cardProps}>
                <Card
                  className="bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 group relative cursor-pointer"
                >
                  {editable && canEditContent && (
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            /* TODO: Open edit modal */
                          }}
                        >
                          <Edit className="w-3 h-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            handleDeleteCard(card._id);
                          }}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                  <CardContent className="p-6">
                    <div className="flex items-start mb-4">
                      <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                        <IconComponent className="w-8 h-8 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-900 mb-2">
                          {card.data.title}
                        </h3>
                      </div>
                    </div>
                    <div
                      className="text-gray-600 leading-relaxed mb-4"
                      dangerouslySetInnerHTML={{ __html: card.data.description }}
                    />
                    {card.data.features && card.data.features.length > 0 && (
                      <ul className="mt-4 space-y-1 mb-4">
                        {card.data.features.map((feature: string, index: number) => (
                          <li key={index} className="text-sm text-gray-600 flex items-center">
                            <div className="w-1.5 h-1.5 bg-blue-600 rounded-full mr-2"></div>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    )}
                    <div className="flex items-center text-blue-600 font-medium group-hover:text-blue-700 transition-colors">
                      <span>Learn More</span>
                      <Icons.ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </CardContent>
                </Card>
              </CardWrapper>
            );
          })}
        </div>

        {serviceCards.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-4">No service cards found.</p>
            {editable && canEditContent && (
              <Button
                onClick={handleAddCard}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add First Service Card
              </Button>
            )}
          </div>
        )}
      </div>
    </section>
  );
};

// Helper function to get grid classes based on variant
function getGridClasses(variant: "homepage" | "services"): string {
  if (variant === "homepage") {
    // Homepage: 3 columns on large screens, wider cards with better spacing
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-items-stretch";
  } else {
    // Services page: 2-3 columns, cards stretch to fill available space
    return "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 justify-items-stretch";
  }
}

export default DynamicServiceCards;
